# Label Support for getParsedMapTasks and getParsedMapForms

## Overview

Added label support to both `getParsedMapTasks` and `getParsedMapForms` functions in `src/terra/utils/helper-composable.js`. These functions now respect the terra store's label configuration settings.

## Changes Made

### 1. getParsedMapTasks Function

The function now:
- Accesses the `settings` from `useTerraStore()`
- Adds the configured label property to feature properties
- Falls back to task name when `label_property_key` is 'name' or not configured

```javascript
// Add label support - use the configured label property key
if (settings?.label_property_key && task[settings.label_property_key]) {
  feature.properties[settings.label_property_key] = task[settings.label_property_key];
}
// Fallback to task name if label_property_key is 'name' or not configured
if (settings?.label_property_key === 'name' || !settings?.label_property_key) {
  feature.properties.name = task.name;
}
```

### 2. getParsedMapForms Function

The function now:
- Accesses the `settings` from `useTerraStore()`
- Adds the configured label property to feature properties
- Falls back to form name when `label_property_key` is 'name' or not configured

```javascript
// Add label support - use the configured label property key
if (settings?.label_property_key && form[settings.label_property_key]) {
  feature.properties[settings.label_property_key] = form[settings.label_property_key];
}
// Fallback to form name if label_property_key is 'name' or not configured
if (settings?.label_property_key === 'name' || !settings?.label_property_key) {
  feature.properties.name = form.name;
}
```

## How It Works

1. **Label Configuration**: The terra store contains a `settings.label_property_key` that determines which property should be used for labels
2. **Dynamic Property Assignment**: Both functions now check if the configured property exists on the task/form object and add it to the feature properties
3. **Fallback Behavior**: If the label property key is 'name' or not configured, the functions fall back to using the task/form name
4. **Integration with Existing Label System**: The existing `toggleLabels()` function in `use-common-helpers.js` will automatically pick up these properties when rendering labels on the map

## Usage Example

```javascript
// Configure label property in terra store
terra_store.settings.label_property_key = 'description'; // or any other property

// When getParsedMapTasks() is called, it will now include:
feature.properties.description = task.description; // if task has description property

// When getParsedMapForms() is called, it will now include:
feature.properties.description = form.description; // if form has description property
```

## Benefits

1. **Consistent Labeling**: Tasks and forms now support the same label configuration as other map features
2. **Flexible Property Selection**: Users can choose any available property for labeling
3. **Backward Compatibility**: Existing functionality remains unchanged when label_property_key is 'name'
4. **Integration**: Works seamlessly with the existing label toggle system

## Testing

The changes have been implemented with proper error handling and fallback mechanisms. A test file has been created at `test/terra-helper-composable.test.js` to verify the functionality.
