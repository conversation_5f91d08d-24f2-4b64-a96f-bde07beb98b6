# Label Support for getParsedMapTasks and getParsedMapForms

## Overview

Added comprehensive label support to both `getParsedMapTasks` and `getParsedMapForms` functions, including separate functions for showing task names and form names on their respective symbols.

## Changes Made

### 1. Terra Store Settings

Added new settings to the terra store:
```javascript
settings: {
  // ... existing settings
  show_tasks_name: false,
  show_forms_name: false,
  // ... other settings
}
```

### 2. getParsedMapTasks Function

The function now:
- Accesses the `settings` from `useTerraStore()`
- Adds the configured label property to feature properties
- Always adds task name for potential use in labels

```javascript
// Add label support - use the configured label property key
if (settings?.label_property_key && task[settings.label_property_key]) {
  feature.properties[settings.label_property_key] = task[settings.label_property_key];
}
// Always add task name for potential use in labels
feature.properties.name = task.name;
```

### 3. getParsedMapForms Function

The function now:
- Accesses the `settings` from `useTerraStore()`
- Adds the configured label property to feature properties
- Always adds form name for potential use in labels

```javascript
// Add label support - use the configured label property key
if (settings?.label_property_key && form[settings.label_property_key]) {
  feature.properties[settings.label_property_key] = form[settings.label_property_key];
}
// Always add form name for potential use in labels
feature.properties.name = form.name;
```

### 4. New Separate Label Functions

#### toggleTasksLabels()
- Creates a separate map layer specifically for task name labels
- Only shows labels when `show_tasks_name` is true
- Uses text offset to avoid overlapping with task icons

#### toggleFormsLabels()
- Creates a separate map layer specifically for form name labels
- Only shows labels when `show_forms_name` is true
- Uses text offset to avoid overlapping with form icons

### 5. Enhanced toggleLabels Function

The main `toggleLabels` function now:
- Includes tasks and forms in the general label system
- Calls the separate task and form label functions
- Supports both general labels and specific task/form name labels

### 6. Label Selector Popup

Updated to include:
- Toggle for "Show tasks name"
- Toggle for "Show forms name"
- Saves these settings to the terra store

## How It Works

1. **General Labels**: When `display_labels` is enabled, tasks and forms are included in the general labeling system using the configured `label_property_key`

2. **Specific Task/Form Labels**: When `show_tasks_name` or `show_forms_name` is enabled, separate map layers are created specifically for showing task and form names

3. **Layer Management**: Each label type has its own map layer to avoid conflicts and allow independent control

4. **Integration**: All label functions are called when the label settings are updated

## Usage Example

```javascript
// Enable general labels with a specific property
terra_store.settings.display_labels = true;
terra_store.settings.label_property_key = 'description';

// Enable specific task name labels
terra_store.settings.show_tasks_name = true;

// Enable specific form name labels
terra_store.settings.show_forms_name = true;

// Call toggleLabels to apply all settings
toggleLabels();
```

## Map Layers Created

1. **features_labels**: General labels for all features including tasks and forms
2. **tasks_labels**: Specific labels for task names only
3. **forms_labels**: Specific labels for form names only

## Benefits

1. **Flexible Labeling**: Users can choose between general labels and specific task/form name labels
2. **Independent Control**: Task and form labels can be toggled independently
3. **No Overlap**: Separate layers prevent label conflicts
4. **Consistent UI**: Integrated with existing label configuration popup
5. **Backward Compatibility**: Existing functionality remains unchanged

## Testing

The changes have been implemented with proper error handling and fallback mechanisms. Functions are exported from the helper composable for use throughout the application.
