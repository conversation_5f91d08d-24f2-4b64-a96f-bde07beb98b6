<script setup>
import { useModal } from 'vue-final-modal';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import TerraWorkflowForm from '~/forms/components/new-form/terra-workflow-form.vue';
import InventoryAssociateTransactionForm from '~/inventory/components/inventory-settings/inventory-associate-transaction-form.vue';
import InventoryCustomFields from '~/inventory/components/inventory-settings/inventory-custom-fields.vue';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';
import WorkflowTemplateDetails from '~/terra/components/workflow/workflow-form-details.vue';

const { $t, common_store, route, router, auth_store } = useCommonImports();

const inventory_store = useInventoryStore();

const terra_workflow_form = useModal({
  component: TerraWorkflowForm,
  attrs: {
    onClose() {
      terra_workflow_form.close();
    },
  },
});

const form$ = ref(null);
const form_data = ref(null);
const is_loading = ref(false);
const form_workflows = ref([]);
const show_note = ref(false);

const members = ref([]);

const transaction_type = computed(() => inventory_store.workflows_map[route.params.transaction_type_uid]);

const from_to = computed(() => {
  const from = transaction_type.value.from_status.map(uid => inventory_store.statuses_map[uid]?.name);
  const to = inventory_store.statuses_map[transaction_type.value.to_status]?.name || 'NA';

  if (from.length === 0)
    return `NA -> ${to}`;
  else if (from.length === 1)
    return `${from[0]} -> ${to}`;
  return `(${from.join(', ')})-> ${to}`;
});
const transactions_type_options = computed(() => {
  return inventory_store.workflows?.filter((workflow) => {
    const remove_current_type = workflow.uid !== route?.params?.transaction_type_uid;
    const associated_workflow_ids = transaction_type.value?.associations?.map(item => item.associated_item_workflow) || [];
    const remove_already_associated = !associated_workflow_ids.includes(workflow.uid);
    const map_item_workflow_destination_source = transaction_type.value?.destination === workflow.source && workflow.from_status.includes(transaction_type.value?.to_status);
    return remove_current_type && remove_already_associated && map_item_workflow_destination_source;
  });
});

const goBack = () => router.back();

async function handleSave() {
  try {
    is_loading.value = true;

    const payload = {
      name: form_data.value.name,
      description: form_data.value.description,
      color: form_data.value.color,
      quantities_label: form_data.value.quantities_label,
      plural_label: form_data.value.plural_label,
      members: members.value,
      notify_members: form_data.value.notify_members,
      form_workflows: form_workflows.value,
      allow_manual_fulfillment: form_data.value.allow_manual_fulfillment,
    };

    await inventory_store.update_workflow({
      uid: transaction_type.value.uid,
      item_workflow: payload,
    });

    is_loading.value = false;
    goBack();
  }
  catch (error) {
    logger.log(`🚀 ~ handleSave ~ error:`, error);
    is_loading.value = false;
  }
}

function updateMembers(e) {
  members.value = ([...e.users, ...e.teams]).map((member) => {
    return {
      ...member,
      asset: route.params.asset_id,
    };
  });
}

function formatString(str) {
  const words = str.split('_');
  words[0] = words[0].charAt(0).toUpperCase() + words[0].slice(1);
  return words.join(' ');
}

function getAssetMembers(members) {
  return members?.filter(member => member?.asset && member?.asset === route?.params?.asset_id) || [];
}

async function openTerraWorkflowForm(index = -1) {
  const form_data = index === -1 ? null : getFormattedFieldDetails(form_workflows.value[index]);
  terra_workflow_form.patchOptions({
    attrs: {
      form_data,
      is_inventory: true,
      disabled_templates: form_workflows.value.map(workflow => workflow.uid),
      onSave(form) {
        const field = {
          uid: form.template,
          name: form.name,
          assignees: form.assignees,
          duration: Number(form.duration),
          required: form.required,
          items: form.items || null,
          type: 'INTG_201',
          assignees_option: form.assignees_option,
        };
        if (index === -1)
          form_workflows.value.push(field);
        else
          form_workflows.value[index] = field;
        terra_workflow_form.close();
      },
    },
  });
  terra_workflow_form.open();
}

function getFormattedFieldDetails(form) {
  return {
    ...form,
    config: {
      template: form.uid,
      assignees: form.assignees,
      duration: form.duration,
      required: form.required || false,
      items: form.items,
      assignees_option: form.assignees_option,
    },
  };
}

const { open: openAssociateTransaction, close: closeAssociateTransaction, patchOptions: patchAssociateTransaction } = useModal({ component: InventoryAssociateTransactionForm });
function associateTransactionHandler(workflow = null) {
  patchAssociateTransaction({
    attrs: {
      associatedItemWorkflow: workflow?.uid && workflow,
      transactionsTypeOptions: !workflow?.uid && transactions_type_options.value,
      onClose: () => {
        closeAssociateTransaction();
      },
      onUpdated: () => {
        show_note.value = true;
        closeAssociateTransaction();
      },
    },
  });
  openAssociateTransaction();
}

onMounted(async () => {
  if (transaction_type.value?.uid) {
    form_data.value = transaction_type.value;
    members.value = getAssetMembers(transaction_type.value.members);
    form_workflows.value = [...(transaction_type.value?.form_workflows || [])];
  }
  await inventory_store.set_items({ query: { asset: route.params.asset_id } });
});
</script>

<template>
  <div class="block w-[calc(100vw_-_100px)]">
    <div class="p-4">
      <div class="flex items-center gap-3 mb-4">
        <HawkButton type="text" icon @click="goBack">
          <IconHawkChevronLeft />
        </HawkButton>
        <div class="text-lg font-semibold text-gray-900">
          {{ transaction_type.name }}
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <Vueform
          ref="form$"
          v-model="form_data"
          :float-placeholders="false"
          :display-errors="false"
          :columns="{
            default: { container: 12, label: 4, wrapper: 12 },
            sm: { container: 12, label: 4, wrapper: 12 },
            md: { container: 12, label: 4, wrapper: 12 },
          }"
          :add-classes="{ ElementLabel: { wrapper: 'text-gray-700 font-medium' } }"
          size="sm"
          sync
          :format-load="data => data"
        >
          <div class="col-span-12">
            <TextElement
              name="name"
              :label="$t('Name')"
              rules="required"
              class="mb-4"
              :placeholder="$t('Enter name')"
            />
            <TextareaElement
              name="description"
              :label="$t('Description')"
              class="mb-4"
              :placeholder="$t('Enter description')"
            />
            <HawkColorInput
              name="color"
              class="mb-4 col-span-12"
              :color="transaction_type?.color || null"
            />
            <StaticElement
              name="stock_operation"
              :label="$t('Operation type')"
              class="mb-4"
            >
              {{ $t(formatString(transaction_type.stock_operation)) }}
            </StaticElement>
            <StaticElement
              name="source_destination"
              class="mb-4"
            >
              <template #label>
                <div class="flex items-center gap-2">
                  {{ $t('From') }} <IconHawkArrowNarrowRight class="w-4 h-4" /> {{ $t('To') }}
                </div>
              </template>
              <div class="flex items-center gap-2">
                {{ formatString(transaction_type.source) }} <IconHawkArrowNarrowRight class="w-4 h-4" /> {{ formatString(transaction_type.destination) }}
              </div>
            </StaticElement>
            <StaticElement
              name="statuses"
              :label="$t('Statuses')"
              :description="$t('The stock will be moved from the above statuses in the same order as they are displayed to the \'Issued\' status on performing the transaction')"
              class="mb-4"
            >
              {{ from_to }}
            </StaticElement>
            <TextElement
              name="quantities_label"
              :label="$t('Quantity label')"
              class="mb-4"
              :placeholder="$t('Enter quantity label')"
            />
            <TextElement
              name="plural_label"
              :label="$t('Plural label')"
              class="mb-4"
              :placeholder="$t('Enter plural label')"
            />
            <HawkAssigneeInput
              v-if="route.params?.asset_id"
              class="w-full mr-5"
              :multi="true"
              :options="{
                name: 'notify_members',
                has_teams: false,
                placeholder: $t('Select users or teams'),
                class: 'mb-4',
                label: $t('Notify'),
                description: $t('Choose users or teams to send notifications on publishing a transaction'),
              }"
            />
            <hr>
            <div class="py-5 grid gap-5">
              <div class="flex justify-between gap-3">
                <div>
                  <div class="font-semibold text-gray-900">
                    {{ $t('Associated transactions types') }}
                  </div>
                  <div class="text-gray-600 text-sm font-normal">
                    {{ $t('Transactions linked to the main transaction, where the source of each associated transaction must match the destination of the main transaction.') }}
                  </div>
                </div>
                <div v-tippy="!transactions_type_options.length ? { content: $t('No transaction type to associate'), placement: 'top' } : ''">
                  <HawkButton :disabled="!transactions_type_options.length" @click="associateTransactionHandler()">
                    <IconHawkPlus />{{ $t('Add') }}
                  </HawkButton>
                </div>
              </div>
              <div class="grid gap-5">
                <template v-if="transaction_type?.associations?.length">
                  <div
                    v-for="association in transaction_type?.associations" :key="association.uid" class="grid grid-cols-12 gap-5 p-4 border rounded-xl hover:shadow-sm hover:bg-gray-25 cursor-pointer"
                    @click="associateTransactionHandler(association)"
                  >
                    <div class="col-span-5">
                      <HawkBadge custom_color="#004EEB" class="whitespace-nowrap">
                        {{ inventory_store.workflows_map[association?.associated_item_workflow]?.name }}
                      </HawkBadge>
                    </div>
                    <div class="col-span-7 flex items-center gap-3 justify-between text-sm">
                      <div class="flex gap-2">
                        <div>
                          {{ $t('Due by') }}:
                        </div>
                        <div>
                          {{ inventory_store.get_custom_field(association?.eta_custom_field)?.name }}
                        </div>
                      </div>
                      <div class="flex gap-2 items-center">
                        <div><span class="mr-2">{{ $t('Reconcile') }}:</span> {{ association?.reconcile ? $t('Yes') : $t('No') }} </div>
                        <IconHawkCheckFour v-if="association?.reconcile" class="size-4" />
                        <HawkFeaturedIcon v-else class="!size-4" size="xs" theme="light-circle" color="error">
                          <IconHawkX />
                        </HawkFeaturedIcon>
                      </div>
                    </div>
                  </div>
                </template>
                <div v-if="transaction_type?.associations?.length" class="font-bold text-sm mb-5 grid gap-1">
                  <span>{{ $t('Note: The changes will be applied for the transaction for the entire organization i.e, across all assets') }}.</span>
                  <div v-if="show_note" class="flex">
                    <span class="invisible mr-1">Note: </span>
                    <span>{{ $t('The changes only apply to the newly published transactions. Previously published transactions will use the configuration available at the time of publish') }}</span>
                  </div>
                </div>
              </div>
              <div class="grid gap-5">
                <div>
                  <div class="font-semibold text-gray-900">
                    {{ $t('Statuses') }}
                  </div>
                  <div class="text-gray-600 text-sm font-normal mr-16">
                    {{ $t('Transaction status will be set automatically according to the tracked quantity in reconciled associated transactions.') }}
                  </div>
                </div>
                <div class="flex text-sm gap-2 mix-blend-multiply">
                  <div class="bg-gray-50 p-1 pl-2 rounded-lg">
                    <span class="mr-1">0%</span>
                    <HawkBadge custom_classes="mix-blend-multiply">
                      {{ $t('Not started') }}
                    </HawkBadge>
                  </div>
                  <div class="bg-green-50 p-1 pl-2 rounded-lg">
                    <span class="mr-1">100%</span>
                    <HawkBadge color="green" custom_classes="mix-blend-multiply">
                      {{ $t('Fulfilled') }}
                    </HawkBadge>
                  </div>
                  <div class="bg-orange-50 p-1 pl-2 rounded-lg">
                    <span class="mr-1">{{ $t('Otherwise') }}</span>
                    <HawkBadge color="orange" custom_classes="mix-blend-multiply">
                      {{ $t('Partially fulfilled') }}
                    </HawkBadge>
                  </div>
                </div>
                <div>
                  <ToggleElement
                    name="allow_manual_fulfillment"
                  >
                    <template #label>
                      <div class="text-sm font-semibold text-gray-700">
                        {{ $t('Allow manual updates') }}
                      </div>
                    </template>
                    <template #after>
                      <div class="text-sm text-gray-600">
                        {{ $t('Enable manual fulfillment status updates for transactions.') }}
                      </div>
                    </template>
                  </ToggleElement>
                </div>
              </div>
            </div>
            <hr>

            <div v-if="auth_store.check_split('inventory_forms_integration')" class="py-4">
              <p class="text-md font-semibold text-gray-900">
                {{ $t('Forms') }}
              </p>
              <p class="text-xs text-gray-600 mb-4">
                {{ $t('Add a form to a transaction or an item to gather data.') }}
              </p>
              <div class="flex flex-col gap-2">
                <WorkflowTemplateDetails
                  v-for="(form, index) in form_workflows"
                  :key="index"
                  class="mb-4"
                  show_template_info
                  :field_details="getFormattedFieldDetails(form)"
                  @edit="openTerraWorkflowForm(index)"
                  @delete="form_workflows.splice(index, 1)"
                >
                  <template #item_details>
                    <div v-if="form.items?.length > 0" class="flex items-center mt-2 flex-wrap gap-2">
                      <div class="text-xs text-gray-600">
                        {{ $t('Items') }}:
                      </div>
                      <HawkBadge v-for="item_uid in form.items.filter(item_uid => !!inventory_store.items_map[item_uid])" :key="item_uid" size="sm" class="!bg-white !border-solid !border !border-gray-300">
                        {{ inventory_store.items_map[item_uid]?.name }}
                      </HawkBadge>
                    </div>
                  </template>
                </WorkflowTemplateDetails>
              </div>
              <HawkButton v-if="auth_store.has_forms_access" type="link" @click="openTerraWorkflowForm()">
                <IconHawkPlus
                  class="text-primary-700 w-4 h-4"
                />
                <div class="text-[14px] font-semibold group-hover:text-primary-800">
                  {{ $t('Add form') }}
                </div>
              </HawkButton>
            </div>

            <template v-if="route.params?.asset_id">
              <hr class="mb-4">
              <div>
                <p class="text-md font-semibold text-gray-900">
                  {{ $t('Permissions') }}
                </p>
                <p class="text-xs text-gray-600 mb-4">
                  {{ $t('Display various types of transaction workflows with detailed information, along with a few configuration options and access controls.') }}
                </p>
                <HawkShare
                  :members="common_store?.filter_users(getAssetMembers(transaction_type?.members))"
                  :teams="common_store?.filter_teams(getAssetMembers(transaction_type?.members))"
                  :access_levels="[
                    ...(common_store.is_development || (auth_store.check_split('inventory_draft_permission') && common_store.is_ril) ? [{ name: 'draft', label: $t('Draft'), description: $t('View own transactions and create drafts only.') }] : []),
                    { name: 'write', label: $t('Publish'), description: $t('View own transactions and create/publish transactions.') },
                  ]"
                  class="mb-5"
                  hide_empty
                  @input="updateMembers"
                />
              </div>
            </template>
            <hr>
          </div>
        </Vueform>
        <InventoryCustomFields :attached-to="{ uid: transaction_type.uid, type: 'item_workflow' }" :has-attach-fields="true" :has-edit-after-publish="true" />
      </div>
      <div class="sticky bottom-0 bg-white">
        <hr>
        <div class="flex justify-end items-center py-5">
          <hawk-button
            class="mr-5"
            type="outlined"
            @click="goBack"
          >
            {{ $t('Cancel') }}
          </hawk-button>
          <hawk-button :loading="is_loading" @click="handleSave">
            {{ $t('Save') }}
          </hawk-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
table {
  // border-collapse: collapse;
  border-radius: 12px;
}

th,
td {
  @apply border-b text-left py-3 px-6;
}

th {
  @apply text-xs font-medium text-gray-600 bg-gray-50 border-t sticky top-0;

  &:first-child {
    @apply  border border-r-0;
  }

  &:last-child {
    @apply border border-l-0 border-b-0;
  }
}

td {
  @apply text-sm;
  &:first-child {
    @apply  border border-r-0;
  }

  &:last-child {
    @apply border border-l-0 border-b-0;
  }
}
</style>
