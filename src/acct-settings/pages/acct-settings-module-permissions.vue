<script setup>
import { ref, computed, onMounted } from 'vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';

const { route, router, auth_store, common_store, $services, $t } = useCommonImports();

const hasChanges = ref(false);

const modules = ref([
  {
    id: 'tasks',
    name: $t('Tasks'),
    description: $t('Record observations, assign, schedule, and monitor crew activities in real time from a single place.'),
    enabled: true
  },
  {
    id: 'terra',
    name: $t('Terra Maps'),
    description: $t('Overlay CAD/KML layers, drone imagery, and GIS data to visualize site layouts, terrain, and progress in an intuitive map and track construction progress.'),
    enabled: true
  },
  {
    id: 'forms',
    name: $t('Forms'),
    description: $t('Convert paper checklists into offline-capable digital forms so surveys and inspections are captured once and synced instantly. Digitalize complex workflows with a no code workflow builder.'),
    enabled: true
  },
  {
    id: 'files',
    name: $t('Files'),
    description: $t('Store drawings, permits, and data sheets in a version-controlled vault with full revision history. Granular permissions, tight access controls, and deep links to tasks and forms.'),
    enabled: true
  },
  {
    id: 'transmittals',
    name: $t('Transmittals'),
    description: $t('Bundle documents, route them for submissions, review, and track approvals without email chaos.'),
    enabled: true
  },
  {
    id: 'pm',
    name: $t('Project management'),
    description: $t('Import Primavera P6 or Microsoft projects schedules and sync them with live field data to surface slippage early. Interactive Gantt and WBS filters let PMs reallocate resources fast, keeping timelines and budgets on track.'),
    enabled: true
  },
  {
    id: 'sm',
    name: $t('System model'),
    description: $t('Build a digital twin linking every pile, table, and inverter to specs, BOM, and inspection data for lifecycle traceability.'),
    enabled: true
  },
  {
    id: 'plans',
    name: $t('Plans'),
    description: $t('Upload multi-page PDFs or CAD plans, mark them up, and pin tasks directly on the sheet.'),
    enabled: true
  },
  {
    id: 'therm',
    name: $t('Therm'),
    description: $t('AI analyzes drone-based thermal scans to classify hot spots, string outages, and other anomalies. Defects are plotted on the map and auto-convert to prioritized remediation tasks for field crews.'),
    enabled: true
  },
  {
    id: 'inventory',
    name: $t('Inventory'),
    description: $t('Barcode-ready tracking follows panels, inverters, and hardware from warehouse receipt to field installation. Track stock in real-time and manage stock-levels.'),
    enabled: true
  }
]);

const isAssetScope = computed(() => !!route.params.asset_id);
const currentAsset = computed(() => isAssetScope.value ? common_store.get_asset(route.params.asset_id) : null);

// Get current disabled modules based on scope
const getCurrentDisabledApps = () => {
  let disabledApps = auth_store.current_organization?.disabled_apps || [];

  if (isAssetScope.value && currentAsset.value) {
    const assetDisabledApps = currentAsset.value.disabled_apps || [];
    // Union of org and asset disabled modules
    disabledApps = [...new Set([...disabledApps, ...assetDisabledApps])];
  }
  return disabledApps;
};

const getOrganizationDisabledApps = () => {
  return auth_store.current_organization?.disabled_apps || [];
};

const filterAndUpdateAppsEnabledStatus = () => {
  const disabledApps = getCurrentDisabledApps();
  const orgDisabledApps = getOrganizationDisabledApps();

  // In asset scope, only show modules that are enabled at org level
  modules.value = modules.value
    .filter(app => {
      if (isAssetScope.value) {
        return !orgDisabledApps.includes(app.id);
      }
      return true;
    })
    .map(app => ({
      ...app,
      enabled: !disabledApps.includes(app.id)
    }));
};

const toggleApp = (app) => {
  app.enabled = !app.enabled;
  checkForChanges();
};

const checkForChanges = () => {
  const currentDisabledApps = modules.value
    .filter(app => !app.enabled)
    .map(app => app.id);

  const originalDisabledApps = getCurrentDisabledApps();

  // Compare current disabled modules with original state
  const currentSet = new Set(currentDisabledApps);
  const originalSet = new Set(originalDisabledApps);

  // Check if any modules were enabled or disabled
  hasChanges.value = currentDisabledApps.length !== originalDisabledApps.length ||
    currentDisabledApps.some(appId => !originalSet.has(appId)) ||
    originalDisabledApps.some(appId => !currentSet.has(appId));
};
const is_saving = ref(false);

const saveChanges = async () => {
  is_saving.value = true;
  try {
    const disabledAppIds = modules.value
      .filter(app => !app.enabled)
      .map(app => app.id);

    if (isAssetScope.value) {
      // Save to asset
      const response = await $services.assets.patch({
        id: route.params.asset_id,
        body: {
          asset: { disabled_apps: disabledAppIds }
        }
      });
      common_store.assets_map[route.params.asset_id] = {
        ...common_store.assets_map[route.params.asset_id],
        disabled_apps: response.data.asset.disabled_apps
      }
    } else {
      // Save to organization
      const response = await $services.organizations.patch({
        id: auth_store.current_organization_uid,
        body: {
          organization: { disabled_apps: disabledAppIds }
        }
      });
      auth_store.logged_in_user_details.organization = {
        ...auth_store.logged_in_user_details.organization,
        disabled_apps: response.data.organization.disabled_apps
      }
    }

    hasChanges.value = false;
    is_saving.value = false;

    filterAndUpdateAppsEnabledStatus();
  } catch (error) {
    is_saving.value = false;
    console.error('Failed to save app permissions:', error);
  }
};

onMounted(() => {
  if (!(auth_store.is_hacker_user || auth_store.is_manager_team_user))
    router.push({ name: "permission-denied" })
  else
    filterAndUpdateAppsEnabledStatus();
});
</script>
<template>
  <div class="py-4">
    <div class="perms-header perms-header-black">
      <div class="flex items-center justify-between pb-4">
        <div>
          <h1 class="text-xl font-semibold text-gray-900">Apps</h1>
          <p class="text-sm text-gray-600 mt-1">
            Enable/disable modules for organizations
          </p>
        </div>
        <HawkButton :disabled="!hasChanges" :loading="is_saving" @click="saveChanges">
          Save changes
        </HawkButton>
      </div>
    </div>

    <div class="space-y-4 pb-[100px]">
      <div v-for="app in modules" :key="app.id"
        class="flex items-center justify-between py-4 px-6 bg-white rounded-lg border border-gray-100">
        <div>
          <h3 class="text-sm font-medium text-gray-900">{{ app.name }}</h3>
          <p class="text-sm text-gray-600 mt-1">{{ app.description }}</p>
        </div>
        <div class="ml-4">
          <HawkToggle :initial-state="app.enabled" icon @click="toggleApp(app)" />
        </div>
      </div>
    </div>
  </div>
</template>
