<script setup>
import { reactive } from 'vue';

const emit = defineEmits(['go-back']);

const chart_types = [
  ['table', 'Table', IconHawkTableTwo],
  ['bar_chart', 'Bar chart', IconHawkHorizontalBarChartOne],
  ['line_chart', 'Line chart', IconHawkLineChartUpOne],
  ['area_chart', 'Area chart', IconHawkAreaChart],
  ['pie_chart', 'Pie chart', IconHawkPieChartThree],
  ['doughnut_chart', 'Doughnut chart', IconHawkDoughnutChart],
  ['scatter_chart', 'Scatter chart', IconHawkScatterChart],
  ['gauge_chart', 'Gauge chart', IconHawkGaugeChart],
  ['heatmap_chart', 'Heatmap chart', IconHawkHeatmapChart],
  ['pyramid_chart', 'Pyramid chart', IconHawkPyramidChart],
  ['funnel_chart', 'Funnel chart', IconHawkFunnelChart],
  ['pareto_chart', 'Pareto chart', IconHawkParetoChart],
  ['waterfall_chart', 'Waterfall chart', IconHawkWaterfallChart],
  ['timeseries_chart', 'Timeseries chart', IconHawkTimeseriesChart],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
    icon: item[2],
  };
});

const state = reactive({
  form_data: {},
  active_item: 'layout',
});

const tabs = computed(() => {
  return [
    { uid: 'layout', label: 'Layout' },
    ...((state.form_data.chart_type === 'gauge_chart') ? [] : [{ uid: 'display', label: 'Display' }]),
    { uid: 'axes', label: 'Axes' },
    { uid: 'advanced', label: 'Advanced' },
  ];
});
</script>

<template>
  <div>
    <div
      class="flex items-center gap-2 cursor-pointer hover:underline text-sm font-semibold text-gray-700"
      @click="emit('go-back')"
    >
      <IconHawkArrowLeft />
      Back to data builder
    </div>

    <Vueform
      v-model="state.form_data"
      size="sm"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
      }"
      class="mt-6"
    >
      <SelectElement
        name="chart_type"
        :label="$t('Chart type')"
        :items="chart_types"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <component :is="option.icon" class="text-gray-500" />
            {{ option.label }}
          </div>
        </template>
        <template #single-label="{ value }">
          <div class="w-full flex items-center gap-2 px-2">
            <component :is="value.icon" class="text-gray-500" />
            {{ value.label }}
          </div>
        </template>
      </SelectElement>
      <HawkTabs v-if="state.form_data.chart_type" :tabs="tabs" :active_item="state.active_item" class="col-span-12 mt-6" @tab-click="state.active_item = $event.uid" />
      <div v-show="state.active_item === 'display'" :key="state.form_data.chart_type" class="col-span-12 flex flex-col gap-y-5">
        <BiChartDisplayTab :chart-type="state.form_data.chart_type" />
      </div>
    </Vueform>
  </div>
</template>
