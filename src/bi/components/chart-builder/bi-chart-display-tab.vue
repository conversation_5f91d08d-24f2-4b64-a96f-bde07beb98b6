<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
});

const show_legend_position = computed(() => ['bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'scatter_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(props.chartType));

const show_values = computed(() => ['bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(props.chartType));

const show_inner_radius = computed(() => ['doughnut_chart'].includes(props.chartType));

const show_symbol_size = computed(() => ['scatter_chart'].includes(props.chartType));

const show_cell_labels = computed(() => ['heatmap_chart'].includes(props.chartType));

const show_labels = computed(() => ['pyramid_chart', 'funnel_chart'].includes(props.chartType));

const show_conversion_rates = computed(() => ['funnel_chart'].includes(props.chartType));

const show_color_palette = computed(() => ['bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'scatter_chart', 'pyramid_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(props.chartType));
</script>

<template>
  <TextElement
    name="title"
    :label="$t('Title')"
    :placeholder="$t('Enter title')"
    description="Main chart title displayed at the top"
  />
  <TextElement
    name="subtitle"
    :label="$t('Subtitle')"
    :placeholder="$t('Enter subtitle')"
    description="Secondary title below the main title"
  />
  <SelectElement
    v-if="show_legend_position"
    name="legend_position"
    label="Legend position"
    :items="{
      none: $t('None'),
      top: $t('Top'),
      bottom: $t('Bottom'),
      left: $t('Left'),
      right: $t('Right'),
    }"
    default="bottom"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <SelectElement
    v-if="show_values"
    name="values"
    :label="$t('Values')"
    :items="{
      show: $t('Show'),
      hide: $t('Hide'),
    }"
    default="hide"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <TextElement
    input-type="number"
    name="precision"
    label="Precision"
    :conditions="[['values', 'show']]"
    placeholder="Enter precision"
  />
  <SliderElement
    v-if="show_inner_radius"
    name="inner_radius"
    label="Inner radius"
    :conditions="[['chart_type', 'doughnut_chart']]"
    :min="20"
    :max="80"
    default="40"
    :step="1"
  />
  <SliderElement
    v-if="show_symbol_size"
    name="symbol_size"
    label="Symbol size"
    :conditions="[['chart_type', 'scatter_chart']]"
    :min="5"
    :max="50"
    default="10"
    :step="1"
  />
  <ToggleElement
    v-if="show_cell_labels"
    name="cell_labels"
    label="Cell labels"
  />
  <ToggleElement
    v-if="show_labels"
    name="labels"
    label="Labels"
  />
  <ToggleElement
    v-if="show_conversion_rates"
    name="conversion_rates"
    label="Conversion rates"
  />
  <template v-if="show_color_palette">
    <SelectElement
      name="color_palette"
      label="Color palette"
      :items="{
        palette1: {
          colors: ['#101828', '#1570EF', '#F97066', '#F79009', '#039855', '#717BBC', '#85E13A'],
        },
        palette2: {
          colors: ['#6366F1', '#EC4899', '#14B8A6', '#F59E0B', '#8B5CF6', '#22C55E', '#EF4444'],
        },
        palette3: {
          colors: ['#84CC16', '#9333EA', '#0EA5E9', '#DC2626', '#10B981', '#F97316', '#4F46E5'],
        },
        palette4: {
          colors: ['#06B6D4', '#D946EF', '#F43F5E', '#3B82F6', '#EAB308', '#8B5CF6', '#059669'],
        },
        dark_palette1: {
          colors: ['#F8FAFC', '#60A5FA', '#FF6B6B', '#FBBF24', '#4ADE80', '#A78BFA', '#FDE047'],
          dark: true,
        },
        dark_palette2: {
          colors: ['#E2E8F0', '#2DD4BF', '#F472B6', '#FB923C', '#22D3EE', '#C084FC', '#86EFAC'],
          dark: true,
        },
        dark_palette3: {
          colors: ['#F1F5F9', '#3B82F6', '#F43F5E', '#F59E0B', '#10B981', '#8B5CF6', '#EAB308'],
          dark: true,
        },
      }"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
      default="palette1"
    >
      <template #option="{ option }">
        <div class="flex items-center gap-2">
          <div class="flex gap-1 p-1" :class="{ 'rounded bg-gray-900': option.label.dark }">
            <div v-for="color in option.label.colors" :key="color" class="w-3 h-3 rounded-full" :style="{ backgroundColor: color }" />
          </div>
        </div>
      </template>
      <template #single-label="{ value }">
        <div class="w-full flex items-center gap-2 px-2">
          <div class="flex gap-1 p-1" :class="{ 'rounded bg-gray-900': value.label.dark }">
            <div v-for="color in value.label.colors" :key="color" class="w-3 h-3 rounded-full" :style="{ backgroundColor: color }" />
          </div>
        </div>
      </template>
    </SelectElement>
  </template>
</template>
