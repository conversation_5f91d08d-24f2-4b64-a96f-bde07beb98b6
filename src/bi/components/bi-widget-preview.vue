<script setup>
import BiBottomDrawer from './bi-bottom-drawer.vue';

const emit = defineEmits(['continue']);
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      WIDGET
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton @click="emit('continue')">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
