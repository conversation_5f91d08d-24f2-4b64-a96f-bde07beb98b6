import { isEqual, uniqBy } from 'lodash-es';
import { useMapboxFeatureSelection } from '~/common/composables/mapbox/selection';
import { useTerraStore } from '~/terra/store/terra.store';
import { useTerraHelperComposable } from '~/terra/utils/helper-composable';

export function useFeatureSelection({ active_tool, layer_ids, store = useTerraStore() }) {
  function formatFeatures(features) {
    return features.map(feature => ({
      geometry: store.features_hash[feature?.properties?.uid]?.geometry || feature.geometry || feature._geometry,
      id: feature.id,
      properties: feature.properties,
      type: feature.type,
    }));
  }

  const { styleSelection, loadSelectionEvents } = useMapboxFeatureSelection({
    layer_ids,
  }, (e, event_type) => {
    if (event_type === 'ctrlSelect') {
      store.clear_gl_draw();
      const filtered_features = store.selected_features.filter(val => val?.properties?.uid !== e[0]?.properties?.uid);
      if (filtered_features.length === store.selected_features.length)
        filtered_features.push(...formatFeatures(e));
      store.selected_features = filtered_features;
      store.terra_track_events('Feature clicked');
    }
    else if (event_type === 'ctrlShiftSelect') {
      const all_selected_features = [...store.selected_features, ...formatFeatures(e)];
      store.selected_features = uniqBy(all_selected_features, f => f.properties?.uid);
    }
    else {
      if (e.length === 0 && store.draw?.getSelected()?.features?.length !== 0)
        return;

      store.selected_features = uniqBy(e, f => f.properties?.uid).filter(f => ['all_features_source'].includes(f.source) || !f.source).map((item) => {
        if (event_type === 'create' && item.geometry.type === 'Polygon') {
          let key = 'is_polygon';
          if (active_tool.value === 'add-rectangle')
            key = 'is_rectangle';
          else if (active_tool.value === 'add-circle')
            key = 'is_circle';
          item.properties.dataProperties = { [key]: true };
        }
        return {
          geometry: (!['create', 'update', 'selectionchange'].includes(event_type)
            ? store.features_hash[item.properties?.uid]?.geometry
            : null) || item.geometry || item._geometry,
          id: item.id,
          properties: store.features_hash[item.properties?.uid]?.properties || item.properties,
          type: item.type,
        };
      });
    }
    if (event_type === 'create' || (event_type === 'update' && store.selected_features.length === 1)) {
      active_tool.value = '';
      store.features_updated = [...store.features_updated, ...store.selected_features.map(item => item.id)];
    }
    if (event_type === 'clicked')
      store.terra_track_events('Feature clicked');
    handleSelectEvents();
  });

  function handleSelectEvents() {
    const count = store.selected_features.length;
    if (count !== 0) {
      const uid = store.selected_features[0]?.properties?.uid;
      // while creating feature
      if (!uid)
        return;
      const is_geometry_changed = isEqual(
        store.selected_features.map(f => [f.id, f.geometry]),
        store.draw?.getAll()?.features?.map(f => [f.id, f.geometry]),
      );
      store.terra_track_events(
        is_geometry_changed ? 'Features geometry changed' : 'Features selected',
        { count, uid },
      );
    }
  }

  return {
    styleSelection,
    loadSelectionEvents,
    handleSelectEvents,
  };
}

/* -------------------------------------------------------------------------- */
/*                                   Labels                                   */
/* -------------------------------------------------------------------------- */
export function toggleLabels() {
  const terra_store = useTerraStore();
  // TODO Move to store and call on feature update/create
  if (terra_store.map) {
    // TODO add this to the top in the order of layers and remove this

    if (terra_store.map.getLayer('features_labels'))
      terra_store.map.removeLayer('features_labels');
    if (terra_store.map.getSource('features_labels'))
      terra_store.map.removeSource('features_labels');
  }
  terra_store.map.loadImage('/img/icons/mapbox-text-bg.png', (error, image) => {
    if (error)
      throw error;
    if (!terra_store.map.hasImage('mapbox-text-bg'))
      terra_store.map.addImage('mapbox-text-bg', image, {});
    if (
      (terra_store.settings.display_labels)
      && terra_store.map

    ) {
      const source = terra_store.map.getSource('features_labels');
      const featureCollection = {
        type: 'FeatureCollection',
        features: terra_store.features_on_map,
      };
      const pointCollection = terra_store.map.getSource('marker-source');
      if (pointCollection) {
        featureCollection.features = [
          ...pointCollection._data.features,
        ];
      }

      // Include tasks and forms in labels if they have the symbol-source
      const symbolCollection = terra_store.map.getSource('symbol-source');
      if (symbolCollection && symbolCollection._data && symbolCollection._data.features) {
        featureCollection.features = [
          ...featureCollection.features,
          ...symbolCollection._data.features.filter(f =>
            f.properties.feature_type === 'task' || f.properties.feature_type === 'form',
          ),
        ];
      }

      const property_key = terra_store.settings.label_property_key;
      featureCollection.features = featureCollection.features.reduce((acc, f) => {
        let sm_properties = {};
        if (f.properties.name)
          sm_properties = terra_store.sm_instances_map.feature_name_hash[f.properties.name];
        const additional_properties = terra_store.get_feature_additional_properties(f);
        const properties = { ...additional_properties, ...(f.properties.extraProperties || {}), ...sm_properties, name: f.properties.name };
        if (!properties?.[property_key])
          return acc;
        f.properties[property_key] = properties[property_key];
        acc.push(f);
        return acc;
      }, []);

      if (source) {
        source.setData(featureCollection);
      }

      else {
        terra_store.map.addSource('features_labels', {
          type: 'geojson',
          data: featureCollection,
        });
      }
      const layer = terra_store.map.getLayer('features_labels');
      if (!layer) {
        terra_store.map.addLayer({
          id: 'features_labels',
          type: 'symbol',

          source: 'features_labels',
          paint: {
            'text-color': '#202',
          },
          layout: {
            'icon-size': 1.2,
            // "text-allow-overlap": true,
            // "icon-allow-overlap": true,
            'icon-image': 'mapbox-text-bg',
            'icon-text-fit': 'both',
            'text-justify': 'center',
            'text-anchor': 'center',
            'text-field': ['get', `${property_key}`],
            'text-font': ['literal', ['Arial Unicode MS Regular']],
            'text-size': 10,
          },
        });
      }
    }
  });

  // Also toggle task and form labels based on their individual settings
  const { toggleTasksLabels, toggleFormsLabels } = useTerraHelperComposable();
  toggleTasksLabels();
  toggleFormsLabels();
}

// Handle toggling of projects and groups
export async function processViewConfig(config, terra_store) {
  if (config?.active_groups?.length) {
    const container_copy = terra_store.container || {};
    const toggleGroup = async (uid) => {
      terra_store.active_group_map[uid].ortho_enabled = true;
      terra_store.active_group_map[uid].features_enabled = true;

      terra_store.projects_request_status.show = true;
      // Update group data for features and
      const group = await terra_store.set_group({ group: terra_store.container.groups[uid], container: terra_store.container });

      terra_store.projects_request_status = {
        total: 0,
        current: 0,
        cancel: false,
        show: false,
      };
      // Replace the group in container with updated group
      container_copy.groups[uid] = group;
    };
    await Promise.all(config.active_groups.map(group_uid => toggleGroup(group_uid)));
    terra_store.set_container({ container: container_copy });
  }
  if (config?.filtered_active_projects?.length) {
    const projects = [];
    config.filtered_active_projects.forEach((project_uid) => {
      const project = terra_store.active_projects_data_map({ all_projects: true })[project_uid];
      terra_store.active_projects_map[project.uid] = {
        ortho_enabled: true,
        features_enabled: true,
      };
      projects.push(project);
    });
    await terra_store.set_projects_essentials({ projects });
  }
}
